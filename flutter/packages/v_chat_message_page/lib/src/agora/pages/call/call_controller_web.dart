// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

import '../../core/call_state.dart';

/// Web-specific implementation that disables Agora functionality
class VCallController extends ValueNotifier<CallState> {
  VCallController(this.dto) : super(CallState()) {
    _showWebNotSupportedMessage();
  }

  final VCallDto dto;
  late BuildContext context;

  void _showWebNotSupportedMessage() {
    // Show a message that calling is not supported on web
    Future.delayed(const Duration(milliseconds: 500), () {
      if (context.mounted) {
        VAppAlert.showErrorSnackBar(
          message: "Voice and video calls are not supported on web platform",
          context: context,
        );
        Navigator.pop(context);
      }
    });
  }

  // Stub methods to maintain compatibility
  void toggleMute() {}
  void toggleSpeaker() {}
  void toggleVideo() {}
  void switchCamera() {}
  void onInviteUsers(BuildContext context) {}

  @override
  void dispose() {
    super.dispose();
  }
}
